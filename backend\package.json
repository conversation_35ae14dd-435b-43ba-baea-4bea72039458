{"name": "giki-virtual-library-backend", "version": "1.0.0", "description": "Backend for GIKI Virtual Library", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "sqlite3": "^5.1.6", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "express-validator": "^6.14.3", "node-cron": "^3.0.2", "nodemailer": "^6.9.1", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^2.0.20"}}