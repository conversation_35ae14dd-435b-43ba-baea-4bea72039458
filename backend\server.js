const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const cors = require('cors');
const { body, validationResult } = require('express-validator');
const cron = require('node-cron');
const nodemailer = require('nodemailer');
const multer = require('multer');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use('/uploads', express.static('uploads'));

// File upload configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    cb(null, Date.now() + '-' + file.originalname);
  }
});
const upload = multer({ storage });

// Database initialization
const db = new sqlite3.Database('library.db', (err) => {
  if (err) {
    console.error('Error opening database:', err);
  } else {
    console.log('Connected to SQLite database');
  }
});

// Create tables
db.serialize(() => {
  // Users table
  db.run(`CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    name TEXT NOT NULL,
    rollNumber TEXT UNIQUE NOT NULL,
    department TEXT NOT NULL,
    profilePicture TEXT,
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  // Books table
  db.run(`CREATE TABLE IF NOT EXISTS books (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    author TEXT NOT NULL,
    isbn TEXT,
    description TEXT,
    category TEXT NOT NULL,
    condition TEXT NOT NULL,
    image TEXT,
    ownerId INTEGER NOT NULL,
    isAvailable BOOLEAN DEFAULT 1,
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ownerId) REFERENCES users (id)
  )`);

  // Borrow requests table
  db.run(`CREATE TABLE IF NOT EXISTS borrowRequests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    bookId INTEGER NOT NULL,
    borrowerId INTEGER NOT NULL,
    ownerId INTEGER NOT NULL,
    status TEXT DEFAULT 'pending',
    requestedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    approvedAt DATETIME,
    dueDate DATETIME,
    returnedAt DATETIME,
    message TEXT,
    FOREIGN KEY (bookId) REFERENCES books (id),
    FOREIGN KEY (borrowerId) REFERENCES users (id),
    FOREIGN KEY (ownerId) REFERENCES users (id)
  )`);

  // Notifications table
  db.run(`CREATE TABLE IF NOT EXISTS notifications (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    userId INTEGER NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL,
    isRead BOOLEAN DEFAULT 0,
    relatedId INTEGER,
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (userId) REFERENCES users (id)
  )`);

  // Reviews table
  db.run(`CREATE TABLE IF NOT EXISTS reviews (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    bookId INTEGER NOT NULL,
    userId INTEGER NOT NULL,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (bookId) REFERENCES books (id),
    FOREIGN KEY (userId) REFERENCES users (id)
  )`);
});

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Auth middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

// Email configuration (for notifications)
const transporter = nodemailer.createTransporter({
  service: 'gmail',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASSWORD
  }
});

// Helper function to create notification
const createNotification = (userId, title, message, type, relatedId = null) => {
  db.run(
    'INSERT INTO notifications (userId, title, message, type, relatedId) VALUES (?, ?, ?, ?, ?)',
    [userId, title, message, type, relatedId]
  );
};

// Routes

// Register
app.post('/api/auth/register', [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 6 }),
  body('name').notEmpty(),
  body('rollNumber').notEmpty(),
  body('department').notEmpty()
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const { email, password, name, rollNumber, department } = req.body;

  // Check if email is GIKI domain
  if (!email.endsWith('@giki.edu.pk')) {
    return res.status(400).json({ error: 'Only GIKI email addresses are allowed' });
  }

  try {
    const hashedPassword = await bcrypt.hash(password, 10);
    
    db.run(
      'INSERT INTO users (email, password, name, rollNumber, department) VALUES (?, ?, ?, ?, ?)',
      [email, hashedPassword, name, rollNumber, department],
      function(err) {
        if (err) {
          if (err.code === 'SQLITE_CONSTRAINT') {
            return res.status(400).json({ error: 'Email or roll number already exists' });
          }
          return res.status(500).json({ error: 'Registration failed' });
        }
        
        const token = jwt.sign({ id: this.lastID, email }, JWT_SECRET, { expiresIn: '7d' });
        res.status(201).json({
          token,
          user: { id: this.lastID, email, name, rollNumber, department }
        });
      }
    );
  } catch (error) {
    res.status(500).json({ error: 'Server error' });
  }
});

// Login
app.post('/api/auth/login', [
  body('email').isEmail().normalizeEmail(),
  body('password').notEmpty()
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const { email, password } = req.body;

  db.get('SELECT * FROM users WHERE email = ?', [email], async (err, user) => {
    if (err) {
      return res.status(500).json({ error: 'Server error' });
    }
    
    if (!user || !await bcrypt.compare(password, user.password)) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    const token = jwt.sign({ id: user.id, email: user.email }, JWT_SECRET, { expiresIn: '7d' });
    res.json({
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        rollNumber: user.rollNumber,
        department: user.department,
        profilePicture: user.profilePicture
      }
    });
  });
});

// Get user profile
app.get('/api/auth/profile', authenticateToken, (req, res) => {
  db.get('SELECT id, email, name, rollNumber, department, profilePicture FROM users WHERE id = ?', 
    [req.user.id], (err, user) => {
    if (err) {
      return res.status(500).json({ error: 'Server error' });
    }
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    res.json(user);
  });
});

// Add a book
app.post('/api/books', authenticateToken, upload.single('image'), [
  body('title').notEmpty(),
  body('author').notEmpty(),
  body('category').notEmpty(),
  body('condition').isIn(['New', 'Like New', 'Good', 'Fair', 'Poor'])
], (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const { title, author, isbn, description, category, condition } = req.body;
  const image = req.file ? req.file.filename : null;

  db.run(
    'INSERT INTO books (title, author, isbn, description, category, condition, image, ownerId) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
    [title, author, isbn, description, category, condition, image, req.user.id],
    function(err) {
      if (err) {
        return res.status(500).json({ error: 'Failed to add book' });
      }
      res.status(201).json({ id: this.lastID, message: 'Book added successfully' });
    }
  );
});

// Get all books
app.get('/api/books', (req, res) => {
  const { search, category, available } = req.query;
  let query = `
    SELECT books.*, users.name as ownerName, users.email as ownerEmail,
           (SELECT AVG(rating) FROM reviews WHERE bookId = books.id) as avgRating,
           (SELECT COUNT(*) FROM reviews WHERE bookId = books.id) as reviewCount
    FROM books 
    JOIN users ON books.ownerId = users.id
  `;
  const params = [];
  const conditions = [];

  if (search) {
    conditions.push('(books.title LIKE ? OR books.author LIKE ?)');
    params.push(`%${search}%`, `%${search}%`);
  }

  if (category) {
    conditions.push('books.category = ?');
    params.push(category);
  }

  if (available === 'true') {
    conditions.push('books.isAvailable = 1');
  }

  if (conditions.length > 0) {
    query += ' WHERE ' + conditions.join(' AND ');
  }

  query += ' ORDER BY books.createdAt DESC';

  db.all(query, params, (err, books) => {
    if (err) {
      return res.status(500).json({ error: 'Server error' });
    }
    res.json(books);
  });
});

// Get book by ID
app.get('/api/books/:id', (req, res) => {
  const bookId = req.params.id;
  
  db.get(`
    SELECT books.*, users.name as ownerName, users.email as ownerEmail,
           (SELECT AVG(rating) FROM reviews WHERE bookId = books.id) as avgRating,
           (SELECT COUNT(*) FROM reviews WHERE bookId = books.id) as reviewCount
    FROM books 
    JOIN users ON books.ownerId = users.id 
    WHERE books.id = ?
  `, [bookId], (err, book) => {
    if (err) {
      return res.status(500).json({ error: 'Server error' });
    }
    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }
    
    // Get reviews for this book
    db.all(`
      SELECT reviews.*, users.name as userName 
      FROM reviews 
      JOIN users ON reviews.userId = users.id 
      WHERE reviews.bookId = ? 
      ORDER BY reviews.createdAt DESC
    `, [bookId], (err, reviews) => {
      if (err) {
        return res.status(500).json({ error: 'Server error' });
      }
      res.json({ ...book, reviews });
    });
  });
});

// Request to borrow a book
app.post('/api/borrow-requests', authenticateToken, [
  body('bookId').isInt(),
  body('message').optional()
], (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const { bookId, message } = req.body;

  // Check if book exists and is available
  db.get('SELECT * FROM books WHERE id = ? AND isAvailable = 1', [bookId], (err, book) => {
    if (err) {
      return res.status(500).json({ error: 'Server error' });
    }
    if (!book) {
      return res.status(404).json({ error: 'Book not available' });
    }
    if (book.ownerId === req.user.id) {
      return res.status(400).json({ error: 'Cannot borrow your own book' });
    }

    // Check if there's already a pending request
    db.get('SELECT * FROM borrowRequests WHERE bookId = ? AND borrowerId = ? AND status = ?', 
      [bookId, req.user.id, 'pending'], (err, existingRequest) => {
      if (err) {
        return res.status(500).json({ error: 'Server error' });
      }
      if (existingRequest) {
        return res.status(400).json({ error: 'You already have a pending request for this book' });
      }

      // Create borrow request
      db.run(
        'INSERT INTO borrowRequests (bookId, borrowerId, ownerId, message) VALUES (?, ?, ?, ?)',
        [bookId, req.user.id, book.ownerId, message || ''],
        function(err) {
          if (err) {
            return res.status(500).json({ error: 'Failed to create request' });
          }

          // Create notification for book owner
          db.get('SELECT name FROM users WHERE id = ?', [req.user.id], (err, borrower) => {
            if (!err && borrower) {
              createNotification(
                book.ownerId,
                'New Borrow Request',
                `${borrower.name} wants to borrow your book "${book.title}"`,
                'borrow_request',
                this.lastID
              );
            }
          });

          res.status(201).json({ id: this.lastID, message: 'Borrow request sent successfully' });
        }
      );
    });
  });
});

// Get borrow requests (for book owners)
app.get('/api/borrow-requests/received', authenticateToken, (req, res) => {
  db.all(`
    SELECT br.*, b.title as bookTitle, b.author as bookAuthor, b.image as bookImage,
           u.name as borrowerName, u.email as borrowerEmail
    FROM borrowRequests br
    JOIN books b ON br.bookId = b.id
    JOIN users u ON br.borrowerId = u.id
    WHERE br.ownerId = ?
    ORDER BY br.requestedAt DESC
  `, [req.user.id], (err, requests) => {
    if (err) {
      return res.status(500).json({ error: 'Server error' });
    }
    res.json(requests);
  });
});

// Get borrow requests made by user
app.get('/api/borrow-requests/sent', authenticateToken, (req, res) => {
  db.all(`
    SELECT br.*, b.title as bookTitle, b.author as bookAuthor, b.image as bookImage,
           u.name as ownerName, u.email as ownerEmail
    FROM borrowRequests br
    JOIN books b ON br.bookId = b.id
    JOIN users u ON br.ownerId = u.id
    WHERE br.borrowerId = ?
    ORDER BY br.requestedAt DESC
  `, [req.user.id], (err, requests) => {
    if (err) {
      return res.status(500).json({ error: 'Server error' });
    }
    res.json(requests);
  });
});

// Approve/Reject borrow request
app.patch('/api/borrow-requests/:id', authenticateToken, [
  body('status').isIn(['approved', 'rejected']),
  body('daysAllowed').optional().isInt({ min: 1, max: 30 })
], (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const requestId = req.params.id;
  const { status, daysAllowed = 14 } = req.body;

  // Get the request
  db.get('SELECT * FROM borrowRequests WHERE id = ? AND ownerId = ?', [requestId, req.user.id], (err, request) => {
    if (err) {
      return res.status(500).json({ error: 'Server error' });
    }
    if (!request) {
      return res.status(404).json({ error: 'Request not found' });
    }
    if (request.status !== 'pending') {
      return res.status(400).json({ error: 'Request already processed' });
    }

    const now = new Date();
    const dueDate = new Date(now);
    dueDate.setDate(dueDate.getDate() + daysAllowed);

    // Update request
    db.run(
      'UPDATE borrowRequests SET status = ?, approvedAt = ?, dueDate = ? WHERE id = ?',
      [status, status === 'approved' ? now.toISOString() : null, 
       status === 'approved' ? dueDate.toISOString() : null, requestId],
      (err) => {
        if (err) {
          return res.status(500).json({ error: 'Failed to update request' });
        }

        if (status === 'approved') {
          // Mark book as not available
          db.run('UPDATE books SET isAvailable = 0 WHERE id = ?', [request.bookId]);
        }

        // Create notification for borrower
        db.get('SELECT title FROM books WHERE id = ?', [request.bookId], (err, book) => {
          if (!err && book) {
            createNotification(
              request.borrowerId,
              status === 'approved' ? 'Request Approved!' : 'Request Rejected',
              status === 'approved' 
                ? `Your request to borrow "${book.title}" has been approved. Due date: ${dueDate.toLocaleDateString()}`
                : `Your request to borrow "${book.title}" has been rejected`,
              'request_response',
              requestId
            );
          }
        });

        res.json({ message: `Request ${status} successfully` });
      }
    );
  });
});

// Return a book
app.patch('/api/borrow-requests/:id/return', authenticateToken, (req, res) => {
  const requestId = req.params.id;

  // Get the request
  db.get('SELECT * FROM borrowRequests WHERE id = ? AND borrowerId = ? AND status = ?', 
    [requestId, req.user.id, 'approved'], (err, request) => {
    if (err) {
      return res.status(500).json({ error: 'Server error' });
    }
    if (!request) {
      return res.status(404).json({ error: 'Active borrow record not found' });
    }
    if (request.returnedAt) {
      return res.status(400).json({ error: 'Book already returned' });
    }

    const now = new Date();

    // Update request as returned
    db.run(
      'UPDATE borrowRequests SET returnedAt = ? WHERE id = ?',
      [now.toISOString(), requestId],
      (err) => {
        if (err) {
          return res.status(500).json({ error: 'Failed to return book' });
        }

        // Mark book as available
        db.run('UPDATE books SET isAvailable = 1 WHERE id = ?', [request.bookId]);

        // Create notification for book owner
        db.get('SELECT title FROM books WHERE id = ?', [request.bookId], (err, book) => {
          if (!err && book) {
            db.get('SELECT name FROM users WHERE id = ?', [req.user.id], (err, borrower) => {
              if (!err && borrower) {
                createNotification(
                  request.ownerId,
                  'Book Returned',
                  `${borrower.name} has returned your book "${book.title}"`,
                  'book_returned',
                  requestId
                );
              }
            });
          }
        });

        res.json({ message: 'Book returned successfully' });
      }
    );
  });
});

// Get notifications
app.get('/api/notifications', authenticateToken, (req, res) => {
  db.all(
    'SELECT * FROM notifications WHERE userId = ? ORDER BY createdAt DESC LIMIT 50',
    [req.user.id],
    (err, notifications) => {
      if (err) {
        return res.status(500).json({ error: 'Server error' });
      }
      res.json(notifications);
    }
  );
});

// Mark notifications as read
app.patch('/api/notifications/mark-read', authenticateToken, (req, res) => {
  db.run(
    'UPDATE notifications SET isRead = 1 WHERE userId = ?',
    [req.user.id],
    (err) => {
      if (err) {
        return res.status(500).json({ error: 'Server error' });
      }
      res.json({ message: 'Notifications marked as read' });
    }
  );
});

// Add review
app.post('/api/reviews', authenticateToken, [
  body('bookId').isInt(),
  body('rating').isInt({ min: 1, max: 5 }),
  body('comment').optional()
], (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const { bookId, rating, comment } = req.body;

  // Check if user has borrowed this book
  db.get(
    'SELECT * FROM borrowRequests WHERE bookId = ? AND borrowerId = ? AND returnedAt IS NOT NULL',
    [bookId, req.user.id],
    (err, borrowRecord) => {
      if (err) {
        return res.status(500).json({ error: 'Server error' });
      }
      if (!borrowRecord) {
        return res.status(400).json({ error: 'You can only review books you have borrowed and returned' });
      }

      // Check if user already reviewed this book
      db.get('SELECT * FROM reviews WHERE bookId = ? AND userId = ?', [bookId, req.user.id], (err, existingReview) => {
        if (err) {
          return res.status(500).json({ error: 'Server error' });
        }
        if (existingReview) {
          return res.status(400).json({ error: 'You have already reviewed this book' });
        }

        // Add review
        db.run(
          'INSERT INTO reviews (bookId, userId, rating, comment) VALUES (?, ?, ?, ?)',
          [bookId, req.user.id, rating, comment || ''],
          function(err) {
            if (err) {
              return res.status(500).json({ error: 'Failed to add review' });
            }
            res.status(201).json({ id: this.lastID, message: 'Review added successfully' });
          }
        );
      });
    }
  );
});

// Get user's books
app.get('/api/my-books', authenticateToken, (req, res) => {
  db.all(`
    SELECT books.*,
           (SELECT AVG(rating) FROM reviews WHERE bookId = books.id) as avgRating,
           (SELECT COUNT(*) FROM reviews WHERE bookId = books.id) as reviewCount,
           (SELECT COUNT(*) FROM borrowRequests WHERE bookId = books.id AND status = 'pending') as pendingRequests
    FROM books 
    WHERE ownerId = ?
    ORDER BY createdAt DESC
  `, [req.user.id], (err, books) => {
    if (err) {
      return res.status(500).json({ error: 'Server error' });
    }
    res.json(books);
  });
});

// Delete book
app.delete('/api/books/:id', authenticateToken, (req, res) => {
  const bookId = req.params.id;

  // Check if user owns the book
  db.get('SELECT * FROM books WHERE id = ? AND ownerId = ?', [bookId, req.user.id], (err, book) => {
    if (err) {
      return res.status(500).json({ error: 'Server error' });
    }
    if (!book) {
      return res.status(404).json({ error: 'Book not found or you do not own this book' });
    }

    // Check if book is currently borrowed
    db.get('SELECT * FROM borrowRequests WHERE bookId = ? AND status = ? AND returnedAt IS NULL', 
      [bookId, 'approved'], (err, activeBorrow) => {
      if (err) {
        return res.status(500).json({ error: 'Server error' });
      }
      if (activeBorrow) {
        return res.status(400).json({ error: 'Cannot delete book that is currently borrowed' });
      }

      // Delete book and related records
      db.serialize(() => {
        db.run('DELETE FROM reviews WHERE bookId = ?', [bookId]);
        db.run('DELETE FROM borrowRequests WHERE bookId = ?', [bookId]);
        db.run('DELETE FROM books WHERE id = ?', [bookId], (err) => {
          if (err) {
            return res.status(500).json({ error: 'Failed to delete book' });
          }
          res.json({ message: 'Book deleted successfully' });
        });
      });
    });
  });
});

// Dashboard stats
app.get('/api/dashboard/stats', authenticateToken, (req, res) => {
  const userId = req.user.id;
  
  db.serialize(() => {
    let stats = {};
    
    // Books owned
    db.get('SELECT COUNT(*) as count FROM books WHERE ownerId = ?', [userId], (err, result) => {
      stats.booksOwned = result ? result.count : 0;
    });
    
    // Books borrowed
    db.get('SELECT COUNT(*) as count FROM borrowRequests WHERE borrowerId = ? AND status = ? AND returnedAt IS NULL', 
      [userId, 'approved'], (err, result) => {
      stats.booksBorrowed = result ? result.count : 0;
    });
    
    // Pending requests received
    db.get('SELECT COUNT(*) as count FROM borrowRequests WHERE ownerId = ? AND status = ?', 
      [userId, 'pending'], (err, result) => {
      stats.pendingRequests = result ? result.count : 0;
    });
    
    // Unread notifications
    db.get('SELECT COUNT(*) as count FROM notifications WHERE userId = ? AND isRead = 0', 
      [userId], (err, result) => {
      stats.unreadNotifications = result ? result.count : 0;
      res.json(stats);
    });
  });
});

// Cron job to check overdue books
cron.schedule('0 9 * * *', () => {
  console.log('Checking for overdue books...');
  
  const now = new Date().toISOString();
  
  db.all(`
    SELECT br.*, b.title as bookTitle, u.name as borrowerName, u.email as borrowerEmail,
           o.name as ownerName
    FROM borrowRequests br
    JOIN books b ON br.bookId = b.id
    JOIN users u ON br.borrowerId = u.id
    JOIN users o ON br.ownerId = o.id
    WHERE br.status = 'approved' 
    AND br.returnedAt IS NULL 
    AND br.dueDate < ?
  `, [now], (err, overdueBooks) => {
    if (err) {
      console.error('Error checking overdue books:', err);
      return;
    }
    
    overdueBooks.forEach(record => {
      // Notify borrower
      createNotification(
        record.borrowerId,
        'Overdue Book Reminder',
        `Your borrowed book "${record.bookTitle}" is overdue. Please return it as soon as possible.`,
        'overdue_reminder',
        record.id
      );
      
      // Notify owner
      createNotification(
        record.ownerId,
        'Overdue Book Alert',
        `Your book "${record.bookTitle}" borrowed by ${record.borrowerName} is overdue.`,
        'overdue_alert',
        record.id
      );
    });
    
    console.log(`Sent overdue notifications for ${overdueBooks.length} books`);
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});